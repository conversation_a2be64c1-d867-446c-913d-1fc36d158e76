<?php

/**
 * demir functions.
 *
 * @package demir
 */

/**
 * Assign the demir version to a var
 */
$theme               = wp_get_theme( 'demir' );
$demir_version = $theme['Version'];
define( 'DEMIR_VERSION', $demir_version );

/**
 * Global Paths
 */
define( 'DEMIR_CORE', get_template_directory() . '/inc/core' );

if ( ! function_exists( 'demir_typography2_enabled' ) ) {
	/**
	 * Determines whether or not to load typography 2.0
	 *
	 * @since 2.0
	 */
	function demir_typography2_enabled() {
		$default = false;
		return (bool) apply_filters( 'demir_typography2_enabled', $default );
	}
}

/**
 * Enqueue scripts and styles.
 */
function demir_scripts() {

	global $demir_version;

	wp_enqueue_script( 'demir-main', get_template_directory_uri() . '/assets/js/main.min.js', array(), $demir_version, true );

	$demir_general_speed_minify_main_css = '';
	$demir_general_speed_minify_main_css = demir_get_option( 'demir_general_speed_minify_main_css' );

	$demir_layout_floating_button_display = '';
	$demir_layout_floating_button_display = demir_get_option( 'demir_layout_floating_button_display' );

	$demir_menu_hover_intent = '';
	$demir_menu_hover_intent = demir_get_option( 'demir_menu_hover_intent' );

	$demir_header_layout = '';
	$demir_header_layout = demir_get_option( 'demir_header_layout' );

	if ( isset( $_GET['header'] ) ) {
		$demir_header_layout = $_GET['header'];
	}

	if ( 'yes' === $demir_general_speed_minify_main_css ) {
		wp_enqueue_style( 'demir-main-min', get_template_directory_uri() . '/assets/css/main/main.min.css', '', $demir_version );
	} else {
		wp_enqueue_style( 'demir-main', get_template_directory_uri() . '/assets/css/main/main.css', '', $demir_version );
	}

	if ( is_singular( 'post' ) || is_archive() || is_author() || is_category() || is_home() ) {
		if ( 'yes' === $demir_general_speed_minify_main_css ) {
			wp_enqueue_style( 'demir-blog-min', get_template_directory_uri() . '/assets/css/main/blog.min.css', '', $demir_version );
		} else {
			wp_enqueue_style( 'demir-blog', get_template_directory_uri() . '/assets/css/main/blog.css', '', $demir_version );
		}
	}

	if ( demir_is_woocommerce_activated() ) {
		if ( is_account_page() ) {
			if ( 'yes' === $demir_general_speed_minify_main_css ) {
				wp_enqueue_style( 'demir-account-min', get_template_directory_uri() . '/assets/css/main/my-account.min.css', '', $demir_version );
			} else {
				wp_enqueue_style( 'demir-account', get_template_directory_uri() . '/assets/css/main/my-account.css', '', $demir_version );
			}
		}
	}

	if ( demir_is_woocommerce_activated() ) {
		if ( is_cart() || is_checkout() ) {
			if ( 'yes' === $demir_general_speed_minify_main_css ) {
				wp_enqueue_style( 'demir-cart-checkout-min', get_template_directory_uri() . '/assets/css/main/cart-checkout.min.css', '', $demir_version );
			} else {
				wp_enqueue_style( 'demir-cart-checkout', get_template_directory_uri() . '/assets/css/main/cart-checkout.css', '', $demir_version );
			}
		}
	}

	if ( 'header-4' == $demir_header_layout ) {
		if ( demir_is_woocommerce_activated() ) {
			wp_enqueue_script( 'demir-dialog', get_template_directory_uri() . '/assets/js/demir-dialog.js', array(), '1.0.1', true );
			if ( 'yes' === $demir_general_speed_minify_main_css ) {
				wp_enqueue_style( 'demir-modal-min', get_template_directory_uri() . '/assets/css/main/modal.min.css', '', $demir_version );
			} else {
				wp_enqueue_style( 'demir-modal', get_template_directory_uri() . '/assets/css/main/modal.css', '', $demir_version );
			}
		}
	}

	if ( demir_is_woocommerce_activated() ) {
		if ( is_product() ) {
			if ( true === $demir_layout_floating_button_display ) {
				wp_enqueue_script( 'demir-dialog', get_template_directory_uri() . '/assets/js/demir-dialog.js', array(), '1.0.1', true );
				if ( 'yes' === $demir_general_speed_minify_main_css ) {
					wp_enqueue_style( 'demir-modal-min', get_template_directory_uri() . '/assets/css/main/modal.min.css', '', $demir_version );
				} else {
					wp_enqueue_style( 'demir-modal', get_template_directory_uri() . '/assets/css/main/modal.css', '', $demir_version );
				}
			}
		}
	}

	if ( demir_is_woocommerce_activated() ) {
		if ( is_product() ) {
			if ( 'yes' === $demir_general_speed_minify_main_css ) {
				wp_enqueue_style( 'demir-product-min', get_template_directory_uri() . '/assets/css/main/product.min.css', '', $demir_version );
			} else {
				wp_enqueue_style( 'demir-product', get_template_directory_uri() . '/assets/css/main/product.css', '', $demir_version );
			}
		}
	}

	if ( is_singular() || is_page() ) {
		if ( comments_open() ) {
			if ( 'yes' === $demir_general_speed_minify_main_css ) {
				wp_enqueue_style( 'demir-comments-min', get_template_directory_uri() . '/assets/css/main/comments.min.css', '', $demir_version );
			} else {
				wp_enqueue_style( 'demir-comments', get_template_directory_uri() . '/assets/css/main/comments.css', '', $demir_version );
			}
		}
	}

	// loading dynamic.css late as inline styles from customizer are added to it.
	wp_enqueue_style( 'demir-dynamic-style', get_template_directory_uri() . '/assets/css/main/dynamic.css', '', $demir_version );

	if ( demir_is_woocommerce_activated() ) {
		if ( is_product() || is_cart() ) {
			wp_enqueue_script( 'demir-quantity', get_template_directory_uri() . '/assets/js/quantity.min.js', array(), '1.1.4', true );
		}
		if ( is_product() ) {
			wp_enqueue_script( 'demir-accordions', get_template_directory_uri() . '/assets/js/pdp-accordions.js', array(), '1.0.0', true );
		}
	}

	// If block editor is active on the frontend.
	if ( function_exists( 'has_blocks' ) ) {
		if ( 'yes' === $demir_general_speed_minify_main_css ) {
				wp_enqueue_style( 'demir-blocks-min', get_template_directory_uri() . '/assets/css/main/blocks.min.css', '', $demir_version );
		} else {
			wp_enqueue_style( 'demir-blocks', get_template_directory_uri() . '/assets/css/main/blocks.css', '', $demir_version );
		}
	}

	// If hover intent theme option is active.
	if ( true === $demir_menu_hover_intent ) {
		wp_enqueue_script( 'demir-hover-intent', get_template_directory_uri() . '/assets/js/sv-hover-intent.min.js', array(), '1.0.0', true );
	}

	// Font Awesome - tum sayfalarda yukle
	wp_enqueue_style( 'font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css', array(), '6.0.0' );

	// AJAX Search CSS ve JS - tum sayfalarda yukle
	wp_enqueue_style( 'demir-ajax-search', get_template_directory_uri() . '/assets/css/ajax-search.css', array(), $demir_version );
	wp_enqueue_script( 'demir-ajax-search', get_template_directory_uri() . '/assets/js/ajax-search.js', array('jquery'), $demir_version, true );

	// AJAX Search parametrelerini JavaScript'e gonder - her zaman yukle
	wp_localize_script( 'demir-ajax-search', 'demir_ajax_search', array(
		'ajax_url' => admin_url( 'admin-ajax.php' ),
		'nonce' => wp_create_nonce( 'demir_ajax_search_nonce' )
	) );

	// WooCommerce AJAX parametrelerini JavaScript'e gonder
	if ( class_exists( 'WooCommerce' ) ) {
		wp_enqueue_script( 'demir-cart-sidebar', get_template_directory_uri() . '/assets/js/cart-sidebar.js', array('jquery'), $demir_version, true );
		wp_localize_script( 'demir-cart-sidebar', 'demir_ajax', array(
			'ajax_url' => admin_url( 'admin-ajax.php' ),
			'nonce' => wp_create_nonce( 'demir_nonce' )
		) );
	}

	$demir_wc_product_category_widget_toggle = '';
	$demir_wc_product_category_widget_toggle = demir_get_option( 'demir_wc_product_category_widget_toggle' );

	if ( demir_is_woocommerce_activated() ) {
		if ( 'enable' === $demir_wc_product_category_widget_toggle ) {
			wp_enqueue_style( 'demir-wc-product-categories-toggle', get_template_directory_uri() . '/assets/css/components/widgets/wc-product-categories-toggle.css', '', $demir_version );
		}
	}

	$demir_cross_sells_carousel 			= '';
	$demir_cross_sells_carousel 			= demir_get_option( 'demir_cross_sells_carousel' );

	if ( demir_is_woocommerce_activated() ) {
		if ( true === $demir_cross_sells_carousel ) {
			wp_enqueue_style( 'demir-cross-sells-carousel', get_template_directory_uri() . '/assets/css/components/pdp/cross-sells-carousel.css', '', $demir_version );
		}
	}

	// If legacy PDP sticky ATC is active.
	$demir_layout_woocommerce_sticky_cart_display = '';
	$demir_layout_woocommerce_sticky_cart_display = demir_get_option( 'demir_layout_woocommerce_sticky_cart_display' );

	if ( demir_is_woocommerce_activated() ) {
		if ( true === $demir_layout_woocommerce_sticky_cart_display ) {
			if ( is_product() ) {
				wp_enqueue_style( 'demir-pdp-legacy-sticky-atc', get_template_directory_uri() . '/assets/css/components/pdp/legacy-sticky-atc.css', '', $demir_version );
			}
		}
	}

}

add_action( 'wp_enqueue_scripts', 'demir_scripts' );


/**
 * Enqueue theme styles within Gutenberg.
 */
function demir_gutenberg_styles() {

	// Load the theme styles within Gutenberg.
	wp_enqueue_style( 'demir-gutenberg', get_template_directory_uri() . '/assets/css/editor/gutenberg.css' );

}
add_action( 'enqueue_block_editor_assets', 'demir_gutenberg_styles' );


/**
 * Theme compatibility.
 */
require get_template_directory() . '/inc/compatibility/compatibility.php';

// Elementor Compatibility requires PHP 5.4 for namespaces.
if ( version_compare( PHP_VERSION, '5.4', '>=' ) ) {
	require get_template_directory() . '/inc/compatibility/elementor-pro/class-demir-elementor-pro.php';
}

/**
 * Excludes some classes from Jetpack's lazy load.
 */
function demir_lazy_exclude( $blacklisted_classes ) {
	$blacklisted_classes = array(
		'skip-lazy',
		'wp-post-image',
		'post-image',
		'wishlist-thumbnail',
		'custom-logo',
	);
	return $blacklisted_classes;

}
add_filter( 'jetpack_lazy_images_blocked_classes', 'demir_lazy_exclude' );

/**
 * TGM Plugin Activation.
 */
require_once DEMIR_CORE . '/functions/class-tgm-plugin-activation.php';
add_action( 'tgmpa_register', 'demir_register_required_plugins' );

/**
 * Recommended plugins
 *
 * @package demir
 */
function demir_register_required_plugins() {
	$plugins = array(
		array(
			'name'     => esc_html__( 'Elementor', 'demir' ),
			'slug'     => 'elementor',
			'required' => false,
		),
		// Kirki is no longer required - using native customizer framework
		// array(
		//	'name'     => esc_html__( 'Kirki', 'demir' ),
		//	'slug'     => 'kirki',
		//	'required' => true,
		// ),

		array(
			'name'     => esc_html__( 'One Click Demo Import', 'demir' ),
			'slug'     => 'one-click-demo-import',
			'required' => false,
		),
		array(
			'name'     => esc_html__( 'WooCommerce', 'demir' ),
			'slug'     => 'woocommerce',
			'required' => false,
		),
	);

	/**
	 * Array of configuration settings.
	 */
	$config = array(
		'domain'       => 'demir',
		'default_path' => '',
		'parent_slug'  => 'themes.php',
		'menu'         => 'tgmpa-install-plugins',
		'has_notices'  => true,
		'is_automatic' => false,
		'message'      => '',
		'strings'      => array(
			'page_title'                      => esc_html__( 'Install Required Plugins', 'demir' ),
			'menu_title'                      => esc_html__( 'Install Plugins', 'demir' ),
			'installing'                      => esc_html__( 'Installing Plugin: %s', 'demir' ),
			'oops'                            => esc_html__( 'Something went wrong with the plugin API.', 'demir' ),
			'notice_can_install_required'     => _n_noop( 'This theme requires the following plugin: %1$s.', 'This theme requires the following plugins: %1$s.', 'demir' ),
			'notice_can_install_recommended'  => _n_noop( 'This theme recommends the following plugin: %1$s.', 'This theme recommends the following plugins: %1$s.', 'demir' ),
			'notice_cannot_install'           => _n_noop( 'Sorry, but you do not have the correct permissions to install the %s plugin. Contact the administrator of this site for help on getting the plugin installed.', 'Sorry, but you do not have the correct permissions to install the %s plugins. Contact the administrator of this site for help on getting the plugins installed.', 'demir' ),
			'notice_can_activate_required'    => _n_noop( 'The following required plugin is currently inactive: %1$s.', 'The following required plugins are currently inactive: %1$s.', 'demir' ),
			'notice_can_activate_recommended' => _n_noop( 'The following recommended plugin is currently inactive: %1$s.', 'The following recommended plugins are currently inactive: %1$s.', 'demir' ),
			'notice_cannot_activate'          => _n_noop( 'Sorry, but you do not have the correct permissions to activate the %s plugin. Contact the administrator of this site for help on getting the plugin activated.', 'Sorry, but you do not have the correct permissions to activate the %s plugins. Contact the administrator of this site for help on getting the plugins activated.', 'demir' ),
			'notice_ask_to_update'            => _n_noop( 'The following plugin needs to be updated to its latest version to ensure maximum compatibility with this theme: %1$s.', 'The following plugins need to be updated to their latest version to ensure maximum compatibility with this theme: %1$s.', 'demir' ),
			'notice_cannot_update'            => _n_noop( 'Sorry, but you do not have the correct permissions to update the %s plugin. Contact the administrator of this site for help on getting the plugin updated.', 'Sorry, but you do not have the correct permissions to update the %s plugins. Contact the administrator of this site for help on getting the plugins updated.', 'demir' ),
			'install_link'                    => _n_noop( 'Begin installing plugin', 'Begin installing plugins', 'demir' ),
			'activate_link'                   => _n_noop( 'Activate installed plugin', 'Activate installed plugins', 'demir' ),
			'return'                          => esc_html__( 'Return to Required Plugins Installer', 'demir' ),
			'plugin_activated'                => esc_html__( 'Plugin activated successfully.', 'demir' ),
			'complete'                        => esc_html__( 'All plugins installed and activated successfully. %s', 'demir' ),
			'nag_type'                        => 'updated',
		),
	);
	tgmpa( $plugins, $config );
}

/**
 * Pre demo content import actions.
 */
function demir_before_demo_import_setup() {

	// Set WC image sizes.
	update_option( 'woocommerce_single_image_width', '800' );
	update_option( 'woocommerce_thumbnail_image_width', '300' );
	update_option( 'woocommerce_thumbnail_cropping', 'uncropped' );

	// Disable Elementor colors and typography.
	update_option( 'elementor_disable_color_schemes', 'yes' );
	update_option( 'elementor_disable_typography_schemes', 'yes' );
}
add_action( 'ocdi/before_content_import', 'demir_before_demo_import_setup' );

/**
 * One Click Importer Demo Data.
 * Note: Demo data URLs have been removed. Please provide your own demo content files.
 */
function demir_import_files() {
	return array(
		// Demo data configuration removed - please add your own demo content files
	);
}

add_filter( 'pt-ocdi/import_files', 'demir_import_files' );

/**
 * Post demo content import actions.
 */
function demir_after_demo_import_setup() {

	// Menus to import and assign.
	$main_menu      = get_term_by( 'name', 'Primary Menu', 'nav_menu' );
	$secondary_menu = get_term_by( 'name', 'Secondary Menu', 'nav_menu' );
	set_theme_mod(
		'nav_menu_locations',
		array(
			'primary'   => $main_menu->term_id,
			'secondary' => $secondary_menu->term_id,
		)
	);

	// Set options for front page and blog page.
	$front_page_id = get_page_by_title( 'Home' );
	$blog_page_id  = get_page_by_title( 'Blog' );

	update_option( 'show_on_front', 'page' );
	update_option( 'page_on_front', $front_page_id->ID );
	update_option( 'page_for_posts', $blog_page_id->ID );

	// Set WC PLP cols to 3.
	update_option( 'woocommerce_catalog_columns', '3' );

	// Re-assign menu items.
	demir_update_menu_items();

	// Set logo (if not already set).
	$custom_logo_id = get_theme_mod( 'custom_logo' );
	if ( ! $custom_logo_id ) {

		// $file     = get_template_directory_uri() . '/assets/images/demir_logo.png';
		$file     = get_template_directory() . '/assets/images/demir_logo.png';
		$contents = file_get_contents( $file );
		$upload   = wp_upload_bits( wp_basename( $file ), null, $contents );

		$type = '';
		if ( ! empty( $upload['type'] ) ) {
			$type = $upload['type'];
		} else {
			$mime = wp_check_filetype( $upload['file'] );
			if ( $mime ) {
				$type = $mime['type'];
			}
		}
		$attachment = array(
			'post_title'     => wp_basename( $upload['file'] ),
			'post_content'   => '',
			'post_type'      => 'attachment',
			'post_mime_type' => $type,
			'guid'           => $upload['url'],
		);

		// Save the attachment.
		$id = wp_insert_attachment( $attachment, $upload['file'] );
		wp_update_attachment_metadata( $id, wp_generate_attachment_metadata( $id, $upload['file'] ) );

		set_theme_mod( 'custom_logo', $id );
	}

	// Force /cart and /checkout to use classic shortcodes for now.
	demir_force_classic_cart_and_checkout_pages();

	esc_html_e( 'demir demo content imported!', 'demir' );
}

add_action( 'pt-ocdi/after_import', 'demir_after_demo_import_setup' );

/**
 * Timeout call.
 */
function demir_change_time_of_single_ajax_call() {
	return 10;
}

add_action( 'pt-ocdi/time_for_one_ajax_call', 'demir_change_time_of_single_ajax_call' );

/**
 * AJAX Search Functionality
 * WooCommerce urunleri icin AJAX tabanli canli arama
 */

// AJAX arama handler fonksiyonu
function demir_ajax_search() {
    // Nonce kontrolu
    if ( ! wp_verify_nonce( $_POST['nonce'], 'demir_ajax_search_nonce' ) ) {
        wp_die( 'Guvenlik kontrolu basarisiz!' );
    }

    $query = sanitize_text_field( $_POST['query'] );

    if ( empty( $query ) || strlen( $query ) < 1 ) {
        wp_send_json_success( array(
            'products' => array(),
            'total' => 0,
            'search_url' => '',
            'query' => $query
        ) );
    }

    // WooCommerce urun arama - basladigini kontrol et
    $args = array(
        'post_type' => 'product',
        'post_status' => 'publish',
        'posts_per_page' => 8,
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_stock_status',
                'value' => 'instock',
                'compare' => '='
            )
        ),
        'tax_query' => array(
            array(
                'taxonomy' => 'product_visibility',
                'field'    => 'name',
                'terms'    => array('exclude-from-search', 'exclude-from-catalog'),
                'operator' => 'NOT IN',
            ),
        ),
    );

    // Ozel arama filtresi ekle - sadece basladigini ara
    add_filter( 'posts_where', 'demir_search_by_title_starts_with', 10, 2 );
    add_filter( 'posts_search', '__return_empty_string' ); // Varsayilan arama devre disi

    $products = new WP_Query( $args );

    // Filtreleri kaldir
    remove_filter( 'posts_where', 'demir_search_by_title_starts_with', 10 );
    remove_filter( 'posts_search', '__return_empty_string' );

    $results = array();

    if ( $products->have_posts() ) {
        while ( $products->have_posts() ) {
            $products->the_post();
            $product = wc_get_product( get_the_ID() );

            if ( $product && $product->is_visible() ) {
                // Urun resmi al
                $image_id = $product->get_image_id();
                $image_url = '';
                if ( $image_id ) {
                    $image_url = wp_get_attachment_image_url( $image_id, 'woocommerce_thumbnail' );
                } else {
                    $image_url = wc_placeholder_img_src( 'woocommerce_thumbnail' );
                }

                $results[] = array(
                    'id' => get_the_ID(),
                    'title' => get_the_title(),
                    'url' => get_permalink(),
                    'price' => $product->get_price_html(),
                    'image' => $image_url,
                    'stock_status' => $product->get_stock_status(),
                    'type' => $product->get_type()
                );
            }
        }
        wp_reset_postdata();
    }

    // Arama sonuclari sayfasinin URL'si
    $search_url = add_query_arg( 's', $query, wc_get_page_permalink( 'shop' ) );

    wp_send_json_success( array(
        'products' => $results,
        'total' => $products->found_posts,
        'search_url' => $search_url,
        'query' => $query
    ) );
}
add_action( 'wp_ajax_demir_ajax_search', 'demir_ajax_search' );
add_action( 'wp_ajax_nopriv_demir_ajax_search', 'demir_ajax_search' );

// Populer aramalar fonksiyonu
function demir_get_popular_searches() {
    // Nonce kontrolu
    if ( ! wp_verify_nonce( $_POST['nonce'], 'demir_ajax_search_nonce' ) ) {
        wp_die( 'Guvenlik kontrolu basarisiz!' );
    }

    // Populer urunleri al (en cok satan veya featured)
    $args = array(
        'post_type' => 'product',
        'post_status' => 'publish',
        'posts_per_page' => 6,
        'meta_query' => array(
            array(
                'key' => '_stock_status',
                'value' => 'instock',
                'compare' => '='
            )
        ),
        'orderby' => 'menu_order',
        'order' => 'ASC'
    );

    $popular_products = new WP_Query( $args );
    $results = array();

    if ( $popular_products->have_posts() ) {
        while ( $popular_products->have_posts() ) {
            $popular_products->the_post();
            $product = wc_get_product( get_the_ID() );

            if ( $product && $product->is_visible() ) {
                $results[] = array(
                    'id' => get_the_ID(),
                    'title' => get_the_title(),
                    'url' => get_permalink(),
                    'type' => 'popular'
                );
            }
        }
        wp_reset_postdata();
    }

    wp_send_json_success( array(
        'searches' => $results,
        'total' => count($results),
        'type' => 'popular'
    ) );
}
add_action( 'wp_ajax_demir_get_popular_searches', 'demir_get_popular_searches' );
add_action( 'wp_ajax_nopriv_demir_get_popular_searches', 'demir_get_popular_searches' );

// Ozel arama filtresi - sadece basladigini ara
function demir_search_by_title_starts_with( $where, $wp_query ) {
    global $wpdb;

    if ( isset( $_POST['query'] ) && ! empty( $_POST['query'] ) ) {
        $search_term = sanitize_text_field( $_POST['query'] );
        $where .= " AND {$wpdb->posts}.post_title LIKE '" . esc_sql( $search_term ) . "%'";
    }

    return $where;
}

/**
 * AJAX fonksiyonlari - Sepet sidebar icin
 */

// Sepet sayisini getir
function demir_get_cart_count() {
    if ( class_exists( 'WooCommerce' ) && WC()->cart ) {
        wp_send_json_success( array(
            'count' => WC()->cart->get_cart_contents_count()
        ) );
    } else {
        wp_send_json_error( 'WooCommerce not available' );
    }
}
add_action( 'wp_ajax_get_cart_count', 'demir_get_cart_count' );
add_action( 'wp_ajax_nopriv_get_cart_count', 'demir_get_cart_count' );

// Mini cart icerigini getir
function demir_get_mini_cart() {
    if ( class_exists( 'WooCommerce' ) ) {
        ob_start();
        woocommerce_mini_cart();
        $mini_cart = ob_get_clean();

        wp_send_json_success( array(
            'mini_cart' => $mini_cart
        ) );
    } else {
        wp_send_json_error( 'WooCommerce not available' );
    }
}
add_action( 'wp_ajax_get_mini_cart', 'demir_get_mini_cart' );
add_action( 'wp_ajax_nopriv_get_mini_cart', 'demir_get_mini_cart' );

// Sepetten urun kaldir
function demir_remove_cart_item() {
    // Nonce kontrolu
    if ( ! wp_verify_nonce( $_POST['nonce'], 'demir_nonce' ) ) {
        wp_send_json_error( 'Nonce verification failed' );
    }

    if ( class_exists( 'WooCommerce' ) && WC()->cart ) {
        $cart_item_key = sanitize_text_field( $_POST['cart_item_key'] );

        if ( WC()->cart->remove_cart_item( $cart_item_key ) ) {
            wp_send_json_success( array(
                'message' => 'Item removed from cart'
            ) );
        } else {
            wp_send_json_error( 'Failed to remove item' );
        }
    } else {
        wp_send_json_error( 'WooCommerce not available' );
    }
}
add_action( 'wp_ajax_remove_cart_item', 'demir_remove_cart_item' );
add_action( 'wp_ajax_nopriv_remove_cart_item', 'demir_remove_cart_item' );

/**
 * Sepet guncellendikinde fragment'lari yenile
 */
function demir_add_to_cart_fragment( $fragments ) {
    ob_start();
    ?>
    <span class="cart-count"><?php echo WC()->cart->get_cart_contents_count(); ?></span>
    <?php
    $fragments['.cart-count'] = ob_get_clean();

    return $fragments;
}
add_filter( 'woocommerce_add_to_cart_fragments', 'demir_add_to_cart_fragment' );

/**
 * Update cart and checkout pages to use classic shortcodes.
 */
function demir_force_classic_cart_and_checkout_pages() {
	$cart_page_id = wc_get_page_id( 'cart' );
	if ( $cart_page_id ) {
		$cart_page = array(
			'ID'           => $cart_page_id,
			'post_content' => '<!-- wp:woocommerce/classic-shortcode /-->',
		);
		wp_update_post( $cart_page );
	}

	$checkout_page_id = wc_get_page_id( 'checkout' );
	if ( $checkout_page_id ) {
		$checkout_page = array(
			'ID'           => $checkout_page_id,
			'post_content' => '<!-- wp:woocommerce/classic-shortcode {"shortcode":"checkout"} /-->',
		);
		wp_update_post( $checkout_page );
	}
}

// Disable generation of smaller images during demo data import.
add_filter( 'pt-ocdi/regenerate_thumbnails_in_content_import', '__return_false' );

// Remove plugin branding.
add_filter( 'pt-ocdi/disable_pt_branding', '__return_true' );

/**
 * Load the Kirki Fallback class.
 */
require get_template_directory() . '/inc/kirki-fallback.php';

/**
 * Load the Native CSS Generator class.
 */
require get_template_directory() . '/inc/class-demir-native-css.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer.php';

// Maybe load Typography 2.0.
$demir_typography2_enabled = demir_typography2_enabled();
if ( $demir_typography2_enabled ) {
	/**
	 * Fonts 2.0 Typography.
	 */
	require get_template_directory() . '/inc/demir-typography.php';

	/**
	 * Fonts 2.0 CSS.
	 */
	require get_template_directory() . '/inc/class-demir-css.php';
	require get_template_directory() . '/inc/demir-cssgen.php';
}

/**
 * Set the content width based on the theme's design and stylesheet.
 */
if ( ! isset( $content_width ) ) {
	$content_width = 1170;
}

$demir = (object) array(
	'version' => $demir_version,

	/**
	 * Initialize all the things.
	 */
	'main'    => require 'inc/class-demir.php',
);

require 'inc/demir-functions.php';
require 'inc/demir-template-hooks.php';
require 'inc/demir-template-functions.php';

/**
 * Load shortcodes.
 */
require 'inc/demir-shortcodes.php';

/**
 * Load metaboxes.
 */
require_once 'inc/metaboxes/demir-metaboxes.php';

if ( demir_is_woocommerce_activated() ) {
	$demir->woocommerce = require 'inc/woocommerce/class-demir-woocommerce.php';
	require 'inc/woocommerce/demir-woocommerce-template-hooks.php';
	// Template functions are now loaded by class-demir-woocommerce.php
}

/**
 * Theme Help page.
 */
require_once get_template_directory() . '/inc/setup/help.php';

/**
 * Inject Critical CSS to wp_head.
 */
function demir_criticalcss() {
	echo '<style>';
	if ( is_front_page() || is_home() ) {
		get_template_part( 'assets/css/criticalcss/home' );
	} elseif ( is_single() ) {
		get_template_part( 'assets/css/criticalcss/single-post' );
	} elseif ( is_page() ) {
		get_template_part( 'assets/css/criticalcss/single-post' );
	} elseif ( is_archive() ) {
		get_template_part( 'assets/css/criticalcss/blog-archive' );
	} elseif ( is_shop() || is_product_category() ) {
		get_template_part( 'assets/css/criticalcss/blog-archive' );
	} elseif ( is_product() ) {
		get_template_part( 'assets/css/criticalcss/single-product' );
	} else {
		get_template_part( 'assets/css/criticalcss/single-post' );
	}
	echo '</style>';
}

/**
 * Get the appropriate handle for css.
 */
function demir_get_css_handle() {

	// Safe Default.
	$css_handle = 'demir-main';

	$demir_general_speed_minify_main_css = '';
	$demir_general_speed_minify_main_css = demir_get_option( 'demir_general_speed_minify_main_css' );

	if ( 'yes' === $demir_general_speed_minify_main_css ) {
		$css_handle = 'demir-main-min';
	} else {
		$css_handle = 'demir-main';
	}

	return $css_handle;
}

/**
 * Replaces a stylesheet link tag with a preload tag.
 *
 * @param string $tag     The link tag as generated by WordPress.
 * @param string $handle  The handle by which the stylesheet is known to WordPress.
 * @param string $href    The URL to the stylesheet, including version number.
 * @param string $media   The media attribute of the stylesheet.
 * @return string The original tag wrapped in a noscript element, followed by the preload tag.
 */
function demir_filter_style_loader_tag( $tag, $handle, $href, $media ) {
	global $wp_styles;

	$demir_css_handle = demir_get_css_handle();

	if ( $demir_css_handle === $handle ) {

		$rel          = 'stylesheet';
		$noscript_tag = $tag;
		$tag          = sprintf(
			'<link rel="preload" as="style" onload="%s" id="%s-css" href="%s" type="text/css" media="%s" />',
			"this.onload=null;this.rel='" . esc_js( $rel ) . "'",
			esc_attr( $handle . '-preload' ),
			esc_url_raw( $href ),
			esc_attr( $media )
		);
		$tag         .= sprintf( '<noscript>%s</noscript>', $noscript_tag );
		$tag         .= '<script>!function(n){"use strict";n.loadCSS||(n.loadCSS=function(){});var o=loadCSS.relpreload={};if(o.support=function(){var e;try{e=n.document.createElement("link").relList.supports("preload")}catch(t){e=!1}return function(){return e}}(),o.bindMediaToggle=function(t){var e=t.media||"all";function a(){t.media=e}t.addEventListener?t.addEventListener("load",a):t.attachEvent&&t.attachEvent("onload",a),setTimeout(function(){t.rel="stylesheet",t.media="only x"}),setTimeout(a,3e3)},o.poly=function(){if(!o.support())for(var t=n.document.getElementsByTagName("link"),e=0;e<t.length;e++){var a=t[e];"preload"!==a.rel||"style"!==a.getAttribute("as")||a.getAttribute("data-loadcss")||(a.setAttribute("data-loadcss",!0),o.bindMediaToggle(a))}},!o.support()){o.poly();var t=n.setInterval(o.poly,500);n.addEventListener?n.addEventListener("load",function(){o.poly(),n.clearInterval(t)}):n.attachEvent&&n.attachEvent("onload",function(){o.poly(),n.clearInterval(t)})}"undefined"!=typeof exports?exports.loadCSS=loadCSS:n.loadCSS=loadCSS}("undefined"!=typeof global?global:this);</script>';
	}

	return $tag;
}

$demir_general_speed_critical_css = '';
$demir_general_speed_critical_css = demir_get_option( 'demir_general_speed_critical_css' );
if ( 'yes' === $demir_general_speed_critical_css ) {
	add_action( 'wp_head', 'demir_criticalcss', 5 );
	add_filter( 'style_loader_tag', 'demir_filter_style_loader_tag', 10, 4 );
}

/**
 * Update menu items with locally installed WC urls.
 */
function demir_update_menu_items() {
	$menu_item_groups = array(
		'Primary Menu'   => array(
			'wc_shop_page'       => array(
				'Shop',
				'All products',
			),
			'wc_checkout_page'   => array(
				'Checkout',
			),
			'wc_my_account_page' => array(
				'My Account',
			),
		),
		'Secondary Menu' => array(
			'wc_my_account_page' => array(
				'My Account',
			),
			'wc_checkout_page'   => array(
				'Checkout',
			),
		),
	);

	foreach ( $menu_item_groups as $menu_item_group_key => $menu_item_group ) {
		foreach ( $menu_item_group as $menu_item_key => $menu_items ) {
			foreach ( $menu_items as $menu_item ) {
				$result = demir_replace_wc_menu_item( $menu_item_group_key, $menu_item_key, $menu_item );
			}
		}
	}
}

/**
 * Helper function to replace wc menu items.
 */
function demir_replace_wc_menu_item( $menu_name, $wc_page_type, $menu_item_name ) {
	$menu_id = demir_wp_menu_id_by_name( $menu_name );
	// get menu items.
	$all_items  = wp_get_nav_menu_items( $menu_id );
	$page_title = $menu_item_name;
	$menu_item  = array_filter(
		$all_items,
		function( $item ) use ( $page_title ) {
			return $item->title == $page_title;
		}
	);

	if ( empty( $menu_item ) ) {
		return;
	}

	$resultcount = count( $menu_item );
	if ( $resultcount == 1 ) {
		if ( 'wc_shop_page' == $wc_page_type ) {
			$wc_page_id = get_option( 'woocommerce_shop_page_id' );
		} elseif ( 'wc_my_account_page' == $wc_page_type ) {
			$wc_page_id = get_option( 'woocommerce_myaccount_page_id' );
		} elseif ( 'wc_cart_page' == $wc_page_type ) {
			$wc_page_id = get_option( 'woocommerce_cart_page_id' );
		} elseif ( 'wc_checkout_page' == $wc_page_type ) {
			$wc_page_id = get_option( 'woocommerce_checkout_page_id' );
		}

		$menu_arr_item           = current( $menu_item );
		$menu_item_id            = $menu_arr_item->ID;
		$menu_item_obj_id        = $menu_arr_item->object_id;
		$menu_item_position      = $menu_arr_item->menu_order;
		$menu_item_parent        = $menu_arr_item->menu_item_parent;
		$menu_item_description   = $menu_arr_item->description;
		$menu_item_title         = $menu_arr_item->post_title;
		$menu_item_classes_array = $menu_arr_item->classes;
		$menu_item_classes       = implode( ',', $menu_item_classes_array );

		if ( $menu_item_obj_id == $wc_page_id ) {
			return;
		}

		$params = array(
			'menu-item-object-id' => $wc_page_id,
			'menu-item-type'      => 'post_type',
			'menu-item-object'    => 'page',
			'menu-item-status'    => 'publish',
		);

		if ( $menu_item_title ) {
			$params['menu-item-title'] = $menu_item_title;
		}

		if ( $menu_item_position ) {
			$params['menu-item-position'] = $menu_item_position;
		}

		if ( $menu_item_parent ) {
			$params['menu-item-parent-id'] = $menu_item_parent;
		}

		if ( $menu_item_description ) {
			$params['menu-item-description'] = $menu_item_description;
		}

		if ( $menu_item_classes ) {
			$params['menu-item-classes'] = $menu_item_classes;
		}

		$result = wp_update_nav_menu_item( $menu_id, $menu_item_id, $params );

	} else {
		return;
	}
}

/**
 * Gets a menu id by name
 *
 * @param string $name The menu name.
 * @return int|boolean The menu id or false if not found
 */
function demir_wp_menu_id_by_name( $name ) {
	$menus = wp_get_nav_menus();

	foreach ( $menus as $menu ) {
		if ( $name === $menu->name ) {
			return $menu->term_id;
		}
	}
	return false;
}


<?php
/**
 * Test page for AJAX search functionality
 */

// Test AJAX search function
function test_demir_ajax_search() {
    // Simulate POST data
    $_POST['query'] = 'test';
    $_POST['nonce'] = wp_create_nonce( 'demir_ajax_search_nonce' );
    
    // Call the function
    demir_ajax_search();
}

// Test popular searches function
function test_demir_get_popular_searches() {
    // Simulate POST data
    $_POST['nonce'] = wp_create_nonce( 'demir_ajax_search_nonce' );
    
    // Call the function
    demir_get_popular_searches();
}

// Test search form HTML
function test_search_form_html() {
    echo '<div class="test-search-container">';
    echo '<h3>Test Search Form:</h3>';
    
    // Test the search form HTML
    ?>
    <div class="site-search ajax-search-container">
        <form role="search" method="get" class="search-form" action="<?php echo esc_url( home_url( '/' ) ); ?>">
            <input type="search" id="ajax-search-input" class="search-field" placeholder="Urun ara..." value="" name="s" />
            <input type="hidden" name="post_type" value="product" />
            <button type="button" class="search-icon-btn">
                <i class="fas fa-search"></i>
            </button>
            <div id="ajax-search-loading"></div>
        </form>
        <div id="ajax-search-results">
            <div class="ajax-search-results-inner"></div>
        </div>
    </div>
    <?php
    
    echo '</div>';
}

// Test JavaScript variables
function test_javascript_vars() {
    echo '<script>';
    echo 'console.log("Testing JavaScript variables:");';
    echo 'console.log("demir_ajax_search:", typeof demir_ajax_search !== "undefined" ? demir_ajax_search : "undefined");';
    echo 'console.log("jQuery:", typeof jQuery !== "undefined" ? "loaded" : "not loaded");';
    echo '</script>';
}

// Run tests if this file is accessed directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    // Include WordPress
    require_once('../../../wp-load.php');
    
    get_header();
    
    echo '<div class="test-page-content">';
    echo '<h1>AJAX Search Test Page</h1>';
    
    // Test 1: Check if functions exist
    echo '<h2>Function Tests:</h2>';
    echo '<p>demir_ajax_search function exists: ' . (function_exists('demir_ajax_search') ? 'YES' : 'NO') . '</p>';
    echo '<p>demir_get_popular_searches function exists: ' . (function_exists('demir_get_popular_searches') ? 'YES' : 'NO') . '</p>';
    echo '<p>WooCommerce active: ' . (class_exists('WooCommerce') ? 'YES' : 'NO') . '</p>';
    
    // Test 2: Display search form
    test_search_form_html();
    
    // Test 3: JavaScript variables
    test_javascript_vars();
    
    echo '</div>';
    
    get_footer();
}
?>

<?php
/**
 *
 * Main menu theme options
 *
 * @package CommerceGurus
 * @subpackage demir
 */

// Main Menu.
$demir_default_options = demir_get_option_defaults();

// Display top bar.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'select',
		'settings'    => 'demir_layout_top_bar_display',
		'label'       => esc_html__( 'Display top bar?', 'demir' ),
		'description' => esc_html__( 'Enable or disable the top bar', 'demir' ),
		'section'     => 'demir_header_section_top_bar',
		'default'     => $demir_default_options['demir_layout_top_bar_display'],
		'priority'    => 10,
		'transport'   => 'refresh',
		'choices'     => array(
			'enable'  => esc_html__( 'Enable', 'demir' ),
			'disable' => esc_html__( 'Disable', 'demir' ),
		),
	)
);

// Show or hide top bar on mobile.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'select',
		'settings'    => 'demir_layout_top_bar_mobile',
		'label'       => esc_html__( 'Hide top bar on mobile?', 'demir' ),
		'section'     => 'demir_header_section_top_bar',
		'default'     => $demir_default_options['demir_layout_top_bar_mobile'],
		'priority'    => 10,
		'active_callback'  => array(
			array(
				'setting'  => 'demir_layout_top_bar_display',
				'value'    => 'enable',
				'operator' => '==',
			),
		),
		'transport'   => 'refresh',
		'choices'     => array(
			'show'  => esc_html__( 'Show', 'demir' ),
			'hide' => esc_html__( 'Hide', 'demir' ),
		),
	)
);

// Top bar padding.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_top_bar_padding',
		'label'       => esc_html__( 'Top bar padding', 'demir' ),
		'description' => esc_html__( 'Adjusts the top and bottom padding.', 'demir' ),
		'section'     => 'demir_header_section_top_bar',
		'default'     => 8,
		'choices'     => array(
			'min'  => 0,
			'max'  => 50,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'  => '.top-bar .textwidget',
				'property' => 'padding-top',
				'units'    => 'px',
				'media_query' => '@media (min-width: 992px)',
			),
			array(
				'element'  => '.top-bar .textwidget',
				'property' => 'padding-bottom',
				'units'    => 'px',
				'media_query' => '@media (min-width: 992px)',
			),
		),
	)
);

// Top bar font size.
demir_Kirki::add_field(
	'demir_config',
	array(
		'type'     => 'typography',
		'settings' => 'demir_top_bar_font_size',
		'label'    => esc_html__( 'Top bar font size', 'demir' ),
		'section'  => 'demir_header_section_top_bar',
		'default'  => array(
			'font-size'      => '14px',
		),
		'priority' => 10,
		'output'   => array(
			array(
				'element'  => '.top-bar',
				'property' => 'font-size',
			),
		),
	)
);


// Header Layout.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'select',
		'settings'    => 'demir_header_layout',
		'label'       => esc_html__( 'Header Layout', 'demir' ),
		'description' => esc_html__( 'Change the header layout', 'demir' ),
		'section'     => 'demir_header_section_layout',
		'default'     => $demir_default_options['demir_header_layout'],
		'priority'    => 10,
		'transport'   => 'refresh',
		'choices'     => array(
			'default'  => esc_html__( 'Logo / Search / Secondary', 'demir' ),
			'header-5' => esc_html__( 'Logo / Search / Secondary / Cart', 'demir' ),
			'header-2' => esc_html__( 'Search / Logo / Secondary', 'demir' ),
			'header-3' => esc_html__( 'Secondary / Logo / Search', 'demir' ),
			'header-4' => esc_html__( 'Logo / Navigation / Cart', 'demir' ),			
		),
	)
);

// Header Layout Contained or Full Width
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'select',
		'settings'    => 'demir_header_layout_container',
		'label'       => esc_html__( 'Header Container', 'demir' ),
		'description' => esc_html__( 'Change the header container', 'demir' ),
		'section'     => 'demir_header_section_layout',
		'default'     => $demir_default_options['demir_header_layout_container'],
		'priority'    => 10,
		'active_callback'  => array(
			array(
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '==',
			),
		),
		'transport'   => 'refresh',
		'choices'     => array(
			'contained'  => esc_html__( 'Contained', 'demir' ),
			'full-width-header' => esc_html__( 'Full width', 'demir' ),
		),
	)
);


// Header Padding Top.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_header_top_padding',
		'label'       => esc_html__( 'Header Top Padding', 'demir' ),
		'description' => esc_html__( 'Adjust the header top padding', 'demir' ),
		'section'     => 'demir_header_section_layout',
		'default'     => 30,
		'priority'    => 1,
		'active_callback'  => array(
			array(
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			),
		),
		'choices'     => array(
			'min'  => 0,
			'max'  => 100,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'     => '.col-full.main-header',
				'property'    => 'padding-top',
				'units'       => 'px',
				'media_query' => '@media (min-width: 993px)',
			),

		),
	)
);

// Header Padding Bottom.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_header_bottom_padding',
		'label'       => esc_html__( 'Header Bottom Padding', 'demir' ),
		'description' => esc_html__( 'Adjust the header bottom padding', 'demir' ),
		'section'     => 'demir_header_section_layout',
		'default'     => 30,
		'priority'    => 1,
		'active_callback'  => array(
			array(
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			),
		),
		'choices'     => array(
			'min'  => 0,
			'max'  => 100,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'     => '.col-full.main-header',
				'property'    => 'padding-bottom',
				'units'       => 'px',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Header Height - Only for header-4 layout.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_header_height',
		'label'       => esc_html__( 'Header Height', 'demir' ),
		'description' => esc_html__( 'Adjust the header height', 'demir' ),
		'section'     => 'demir_header_section_layout',
		'default'     => 90,
		'priority'    => 1,
		'active_callback'  => array(
			array(
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '==',
			),
		),
		'choices'     => array(
			'min'  => 0,
			'max'  => 200,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'     => '.header-4 .header-4-container',
				'property'    => 'height',
				'units'       => 'px',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'     => '.header-4 .menu-primary-menu-container > ul > li > a, .header-4 .menu-primary-menu-container > ul > li.nolink, .header-4 .search-trigger',
				'property'    => 'line-height',
				'units'       => 'px',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Display the search.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'select',
		'settings'    => 'demir_layout_search_display',
		'label'       => esc_html__( 'Display the search?', 'demir' ),
		'description' => esc_html__( 'Enable or disable the search. (Ajaxify your product search in CommerceKit!)', 'demir' ),
		'section'     => 'demir_header_section_layout',
		'default'     => $demir_default_options['demir_layout_search_display'],
		'priority'    => 10,
		'transport'   => 'refresh',
		'choices'     => array(
			'enable'  => esc_html__( 'Product Search', 'demir' ),
			'advanced-woo-search'  => esc_html__( 'Advanced Woo Search Plugin', 'demir' ),
			'ajax-search-wc'  => esc_html__( 'FiboSearch Plugin', 'demir' ),
			'smart-search-pro'  => esc_html__( 'Smart Search PRO Plugin', 'demir' ),
			'yith-search'  => esc_html__( 'YITH WooCommerce Ajax Search Plugin', 'demir' ),
			'demir-ajax-search'  => esc_html__( 'Demir AJAX Search', 'demir' ),
			'regular'  => esc_html__( 'Regular Search', 'demir' ),
			'disable' => esc_html__( 'Disable', 'demir' ),
		),
	)
);

// Search title. Only if header-4 is selected.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'text',
		'settings'  => 'demir_layout_search_title',
		'label'     => esc_html__( 'Search modal title', 'demir' ),
		'section'   => 'demir_header_section_layout',
		'default'   => $demir_default_options['demir_layout_search_title'],
		'priority'  => 10,
		'transport' => 'auto',
		'active_callback'  => [
			[
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '==',
			],
		],	
		'js_vars'   => array(
			array(
				'element'  => '.search-modal-heading',
				'function' => 'html',
			),
		),
	)
);

// Search style.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'select',
		'settings' => 'demir_layout_search_display_type',
		'label'    => esc_attr__( 'Search design', 'demir' ),
		'section'  => 'demir_header_section_layout',
		'default'  => $demir_default_options['demir_layout_search_display_type'],
		'active_callback'  => array(
			array(
				'setting'  => 'demir_layout_search_display',
				'value'    => 'disable',
				'operator' => '!==',
			),
		),
		'choices'  => array(
			'default' => esc_attr__( 'Filled', 'demir' ),
			'outline'  => esc_attr__( 'Outline', 'demir' ),
		),
		'priority' => 10,
	)
);

// Display My Account icon on desktop.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_layout_myaccount_display',
		'label'     => esc_html__( 'Display account icon on desktop', 'demir' ),
		'section'   => 'demir_header_section_layout',
		'default'  => $demir_default_options['demir_layout_myaccount_display'],
		'choices'  => array(
			'enable' => esc_attr__( 'Enable', 'demir' ),
			'disable'  => esc_attr__( 'Disable', 'demir' ),

		),
		'priority' => 10,
		'active_callback'  => array(
			array(
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '==',
			),
		),
	)
);


// Navigation Height.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_navigation_height',
		'label'       => esc_html__( 'Navigation Height', 'demir' ),
		'description' => esc_html__( 'Adjust the navigation height', 'demir' ),
		'section'     => 'demir_navigation_section_layout',
		'default'     => 60,
		'priority'    => 1,
		'active_callback'  => [
			[
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			],
		],	
		'choices'     => array(
			'min'  => 0,
			'max'  => 200,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'  => '.menu-primary-menu-container > ul > li > a, .menu-primary-menu-container > ul > li.nolink > span, .site-header-cart, .logo-mark',
				'property' => 'line-height',
				'units'    => 'px',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'  => '.site-header-cart, .menu-primary-menu-container > ul > li.menu-button',
				'property' => 'height',
				'units'    => 'px',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Enable hover intent.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_menu_hover_intent',
		'label'     => esc_html__( 'Enable hover intent', 'demir' ),
		'description' => esc_html__( 'Tracks cursor movement to interpret when it is likely a user intended to hover over menu', 'demir' ),
		'section'   => 'demir_navigation_section_layout',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);


// Display menu descriptions
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_menu_display_description',
		'label'     => esc_html__( 'Display menu descriptions', 'demir' ),
		'section'   => 'demir_navigation_section_layout',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);


// Sticky Navigation.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'select',
		'settings'    => 'demir_sticky_header',
		'label'       => esc_html__( 'Sticky Navigation', 'demir' ),
		'description' => esc_html__( 'Stick the navigation on scroll', 'demir' ),
		'section'     => 'demir_header_section_layout',
		'default'     => $demir_default_options['demir_sticky_header'],
		'priority'    => 10,
		'transport'   => 'refresh',
		'choices'     => array(
			'enable'  => esc_html__( 'Enable', 'demir' ),
			'disable' => esc_html__( 'Disable', 'demir' ),
		),
	)
);

// Mobile Sticky Header
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'select',
		'settings' => 'demir_sticky_mobile_header',
		'label'    => esc_attr__( 'Mobile Sticky Header', 'demir' ),
		'section'  => 'demir_section_general_mobile_header',
		'default'  => $demir_default_options['demir_sticky_mobile_header'],
		'choices'  => array(
			'enable' => esc_attr__( 'Enable', 'demir' ),
			'disable'  => esc_attr__( 'Disable', 'demir' ),

		),
		'priority' => 10,
	)
);


// Main Navigation Links Color.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'color',
		'settings'  => 'demir_navigation_color',
		'label'     => esc_html__( 'Navigation links', 'demir' ),
		'section'   => 'demir_color_section_navigation',
		'default'   => $demir_default_options['demir_navigation_color'],
		'priority'  => 10,
		'active_callback'  => [
			[
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			],
		],	
		'output'    => array(
			array(
				'element'     => '.menu-primary-menu-container > ul > li > a, .menu-primary-menu-container > ul > li.nolink > span',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'     => '.main-navigation ul.menu > li.menu-item-has-children > a::after',
				'property'    => 'background-color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'     => '.menu-primary-menu-container > ul > li > a, .menu-primary-menu-container > ul > li.nolink > span',
				'function'    => 'css',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'     => '.main-navigation ul.menu > li.menu-item-has-children > a::after',
				'property'    => 'background-color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Header 4 (One row) Navigation Links Color.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'color',
		'settings'  => 'demir_navigation_color_header_4',
		'label'     => esc_html__( 'Navigation links', 'demir' ),
		'section'   => 'demir_color_section_navigation',
		'default'   => $demir_default_options['demir_navigation_color_header_4'],
		'priority'  => 10,
		'active_callback'  => [
			[
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '==',
			],
		],	
		'output'    => array(
			array(
				'element'     => '.header-4 .menu-primary-menu-container > ul > li > a, .header-4 .demir-cart .cart-contents .amount, .header-4 .search-trigger, .header-4 .search-trigger:hover, .header-4 .search-trigger:focus, .demir-myaccount a, .demir-myaccount a:hover',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'     => '.header-4 .main-navigation ul.menu > li.menu-item-has-children > a::after, .header-4 .main-navigation ul.menu > li.page_item_has_children > a::after, .header-4 .main-navigation ul.nav-menu > li.menu-item-has-children > a::after, .header-4 .main-navigation ul.nav-menu > li.page_item_has_children > a::after',
				'property'    => 'background-color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'     => '.header-4 .menu-primary-menu-container > ul > li > a, .header-4 .demir-cart .cart-contents .amount, .header-4 .search-trigger, .header-4 .search-trigger:hover, .header-4 .search-trigger:focus, .demir-myaccount a, .demir-myaccount a:hover',
				'function'    => 'css',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'     => '.header-4 .main-navigation ul.menu > li.menu-item-has-children > a::after, .header-4 .main-navigation ul.menu > li.page_item_has_children > a::after, .header-4 .main-navigation ul.nav-menu > li.menu-item-has-children > a::after, .header-4 .main-navigation ul.nav-menu > li.page_item_has_children > a::after',
				'property'    => 'background-color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Main Navigation Links Hover/Selected Color.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'color',
		'settings'  => 'demir_navigation_color_hover',
		'label'     => esc_html__( 'Navigation links hover/selected', 'demir' ),
		'section'   => 'demir_color_section_navigation',
		'default'   => $demir_default_options['demir_navigation_color_hover'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'  => '.menu-primary-menu-container > ul > li > a span:before, .menu-primary-menu-container > ul > li.nolink > span:before',
				'property' => 'border-color',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.menu-primary-menu-container > ul > li > a span:before, .menu-primary-menu-container > ul > li.nolink > span:before',
				'property' => 'border-color',
			),
		),
	)
);

// Fade out other menu items on hover.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_navigation_color_other_hover',
		'label'       => esc_html__( 'Fade out other links on hover', 'demir' ),
		'description' => esc_html__( 'Opacity (%).', 'demir' ),
		'section'     => 'demir_color_section_navigation',
		'default'     => 0.65,
		'priority'    => 1,
		'active_callback'  => array(
			array(
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			),
		),
		'choices'     => array(
			'min'  => 0,
			'max'  => 1,
			'step' => 0.01,
		),
		'output'      => array(
			array(
				'element'  => '.menu-primary-menu-container > ul.menu:hover > li > a',
				'property' => 'opacity',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);


demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_colors_navigation_heading_1',
		'section'  => 'demir_color_section_navigation',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Dropdowns', 'demir' ) . '</div>',
		'priority' => 10,
	)
);


// Navigation Dropdown Background Color.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'color',
		'settings'  => 'demir_navigation_dropdown_background',
		'label'     => esc_html__( 'Navigation dropdown background', 'demir' ),
		'section'   => 'demir_color_section_navigation',
		'default'   => $demir_default_options['demir_navigation_dropdown_background'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'     => '.main-navigation ul.menu ul.sub-menu',
				'property'    => 'background-color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'     => '.main-navigation ul.menu ul.sub-menu',
				'function'    => 'css',
				'property'    => 'background-color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Navigation Dropdown Color.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'color',
		'settings'  => 'demir_navigation_dropdown_color',
		'label'     => esc_html__( 'Navigation dropdown text', 'demir' ),
		'section'   => 'demir_color_section_navigation',
		'default'   => $demir_default_options['demir_navigation_dropdown_color'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'     => '.main-navigation ul.menu ul li a, .main-navigation ul.nav-menu ul li a',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'     => '.main-navigation ul.menu ul li a, .main-navigation ul.nav-menu ul li a',
				'function'    => 'css',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Main Navigation Dropdown Hover Color.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'color',
		'settings'  => 'demir_navigation_dropdown_hover_color',
		'label'     => esc_html__( 'Navigation dropdown hover', 'demir' ),
		'section'   => 'demir_color_section_navigation',
		'default'   => $demir_default_options['demir_navigation_dropdown_hover_color'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'     => '.main-navigation ul.menu ul li.menu-item:not(.menu-item-image):not(.heading) > a:hover',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'     => '.main-navigation ul.menu ul li.menu-item:not(.menu-item-image):not(.heading) > a:hover',
				'function'    => 'css',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_colors_navigation_heading_2',
		'section'  => 'demir_color_section_navigation',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Secondary Navigation', 'demir' ) . '</div>',
		'priority' => 10,
	)
);


// Secondary Navigation Color.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'color',
		'settings'  => 'demir_secondary_navigation_color',
		'label'     => esc_html__( 'Secondary navigation color', 'demir' ),
		'section'   => 'demir_color_section_navigation',
		'default'   => $demir_default_options['demir_secondary_navigation_color'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'  => '.secondary-navigation .menu a, .ri.menu-item:before, .fa.menu-item:before',
				'property' => 'color',
			),
			array(
				'element'  => '.secondary-navigation .icon-wrapper svg',
				'property' => 'stroke',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.secondary-navigation .menu a, .ri.menu-item:before, .fa.menu-item:before',
				'function' => 'css',
				'property' => 'color',
			),
			array(
				'element'  => '.secondary-navigation .icon-wrapper svg',
				'property' => 'stroke',
			),
		),
	)
);

demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_colors_navigation_heading_3',
		'section'  => 'demir_color_section_navigation',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Cart', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Navigation Cart Icon Color.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'color',
		'settings'  => 'demir_cart_icon_color',
		'label'     => esc_html__( 'Cart icon', 'demir' ),
		'section'   => 'demir_color_section_navigation',
		'default'   => $demir_default_options['demir_cart_icon_color'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'  => '.demir-cart a.cart-contents .count, .demir-cart a.cart-contents .count:after',
				'property' => 'border-color',
			),
			array(
				'element'  => '.demir-cart a.cart-contents .count, .demir-cart-icon i',
				'property' => 'color',
			),
			array(
				'element'  => '.demir-cart a.cart-contents:hover .count, .demir-cart a.cart-contents:hover .count',
				'property' => 'background-color',
			),
			array(
				'element'  => '.demir-cart-icon svg',
				'property' => 'stroke',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.demir-cart a.cart-contents .count, .demir-cart a.cart-contents .count:after',
				'function' => 'css',
				'property' => 'border-color',
			),
			array(
				'element'  => '.demir-cart a.cart-contents .count, .demir-cart-icon i',
				'property' => 'color',
			),
			array(
				'element'  => '.demir-cart a.cart-contents:hover .count, .demir-cart a.cart-contents:hover .count',
				'property' => 'background-color',
			),
			array(
				'element'  => '.demir-cart-icon svg',
				'property' => 'stroke',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Navigation Cart Text Color.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'color',
		'settings'  => 'demir_cart_color',
		'label'     => esc_html__( 'Cart text', 'demir' ),
		'section'   => 'demir_color_section_navigation',
		'default'   => $demir_default_options['demir_cart_color'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'  => '.demir-cart .cart-contents',
				'property' => 'color',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.demir-cart .cart-contents',
				'function' => 'css',
				'property' => 'color',
			),
		),
	)
);

// Navigation Cart Hover Text Color.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'color',
		'settings'  => 'demir_cart_hover_color',
		'label'     => esc_html__( 'Cart text hover color', 'demir' ),
		'section'   => 'demir_color_section_navigation',
		'default'   => $demir_default_options['demir_cart_hover_color'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'  => '.demir-cart a.cart-contents:hover .count',
				'property' => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.demir-cart a.cart-contents:hover .count',
				'function' => 'css',
				'property' => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Cart Quantity Bubble Background Color.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'color',
		'settings'  => 'demir_cart_bubble_background_color',
		'label'     => esc_html__( 'Cart quantity background color', 'demir' ),
		'section'   => 'demir_color_section_navigation',
		'default'   => $demir_default_options['demir_cart_bubble_background_color'],
		'active_callback'  => array(
			array(
				'setting'  => 'demir_layout_woocommerce_cart_icon',
				'value'    => 'basket',
				'operator' => '!==',
			),
		),
		'priority'  => 10,
		'output'    => array(
			array(
				'element'  => '.demir-cart a.cart-contents .demir-cart-icon .mini-count',
				'property' => 'background-color',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.demir-cart a.cart-contents .demir-cart-icon .mini-count',
				'function' => 'css',
				'property' => 'background-color',
			),
		),
	)
);

// Cart Quantity Bubble Border Color.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'color',
		'settings'  => 'demir_cart_bubble_border_color',
		'label'     => esc_html__( 'Cart quantity border color', 'demir' ),
		'section'   => 'demir_color_section_navigation',
		'default'   => $demir_default_options['demir_cart_bubble_border_color'],
		'active_callback'  => array(
			array(
				'setting'  => 'demir_layout_woocommerce_cart_icon',
				'value'    => 'basket',
				'operator' => '!==',
			),
		),
		'priority'  => 11,
		'output'    => array(
			array(
				'element'  => '.demir-cart a.cart-contents .demir-cart-icon .mini-count',
				'property' => 'border-color',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.demir-cart a.cart-contents .demir-cart-icon .mini-count',
				'function' => 'css',
				'property' => 'border-color',
			),
		),
	)
);

// Display Cart in Menu.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_layout_woocommerce_display_cart',
		'label'     => esc_html__( 'Display cart', 'demir' ),
		'section'   => 'demir_cart_section_layout',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Cart Icon.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'select',
		'settings'    => 'demir_layout_woocommerce_cart_icon',
		'label'       => esc_html__( 'Cart icon', 'demir' ),
		'description' => esc_html__( 'If adjusting, test in an incognito window', 'demir' ),
		'section'     => 'demir_cart_section_layout',
		'default'     => $demir_default_options['demir_layout_woocommerce_cart_icon'],
		'priority'    => 10,
		'transport'   => 'refresh',
		'choices'     => array(
			'basket'  => esc_html__( 'Basket (Default)', 'demir' ),
			'cart' => esc_html__( 'Cart icon', 'demir' ),
			'bag' => esc_html__( 'Bag icon', 'demir' ),
		),
	)
);

// Cart sidebar Title.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'text',
		'settings'  => 'demir_cart_title',
		'label'     => esc_html__( 'Cart sidebar title', 'demir' ),
		'section'   => 'demir_cart_section_layout',
		'default'   => $demir_default_options['demir_cart_title'],
		'priority'  => 10,
		'transport' => 'auto',
		'js_vars'   => array(
			array(
				'element'  => '.cart-drawer-heading',
				'function' => 'html',
			),
		),
	)
);

// Cart sidebar - display quantity.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_minicart_quantity',
		'label'     => esc_html__( 'Cart sidebar quantity arrows', 'demir' ),
		'description' => esc_html__( 'Display quantity arrows in mini cart', 'demir' ),
		'section'   => 'demir_cart_section_layout',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Cart sidebar - hide view cart link.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'toggle',
		'settings'  => 'demir_sidebar_hide_cart_link',
		'label'     => esc_html__( 'Cart sidebar - hide "View Cart"', 'demir' ),
		'section'   => 'demir_cart_section_layout',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Cart sidebar below text.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'textarea',
		'settings'  => 'demir_cart_below_text',
		'label'     => esc_html__( 'Cart sidebar below text', 'demir' ),
		'section'   => 'demir_cart_section_layout',
		'default'   => $demir_default_options['demir_cart_below_text'],
		'priority'  => 10,
		'transport' => 'auto',
		'js_vars'   => array(
			array(
				'element'  => '.cart-drawer-below',
				'function' => 'html',
			),
		),
	)
);

// Below header padding.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_below_header_padding',
		'label'       => esc_html__( 'Below header padding', 'demir' ),
		'description' => esc_html__( 'Adjusts the top and bottom padding.', 'demir' ),
		'section'     => 'demir_below_header_section_layout',
		'default'     => 12,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 50,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'  => '.header-widget-region .widget',
				'property' => 'padding-top',
				'units'    => 'px',
			),
			array(
				'element'  => '.header-widget-region .widget',
				'property' => 'padding-bottom',
				'units'    => 'px',
			),
		),
	)
);

// Below header font size.
demir_Kirki::add_field(
	'demir_config',
	array(
		'type'     => 'typography',
		'settings' => 'demir_below_header_font_size',
		'label'    => esc_html__( 'Below header font size', 'demir' ),
		'section'  => 'demir_below_header_section_layout',
		'default'  => array(
			'font-size'      => '14px',
		),
		'priority' => 10,
		'output'   => array(
			array(
				'element'  => '.header-widget-region',
				'property' => 'font-size',
			),
		),
	)
);



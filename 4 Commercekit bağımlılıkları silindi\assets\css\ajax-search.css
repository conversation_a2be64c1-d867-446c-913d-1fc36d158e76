/**
 * AJAX Search Styles
 */

/* Search Container */
.ajax-search-container {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    display: block !important;
    visibility: visible !important;
}

/* Debug styles */
.ajax-search-container {
    border: 2px solid red !important;
    background: yellow !important;
    padding: 10px !important;
}

/* Search Form */
.ajax-search-container .search-form {
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
}

.ajax-search-container .search-field {
    width: 100%;
    padding: 10px 40px 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: all 0.3s ease;
    outline: none;
}

.ajax-search-container.focused .search-field {
    border-color: #666;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

/* Search Icon Button */
.ajax-search-container .search-icon-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: #666;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.ajax-search-container .search-icon-btn:hover {
    color: #333;
}

/* Loading Indicator */
#ajax-search-loading {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top: 2px solid #333;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    display: none;
}

#ajax-search-loading.show {
    display: block;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Search Results Dropdown */
#ajax-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: #fff;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    display: none;
}

#ajax-search-results.show {
    display: block;
}

.ajax-search-results-inner {
    padding: 10px;
}

/* Search Item */
.ajax-search-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #f0f0f0;
    text-decoration: none;
    color: #333;
    transition: background-color 0.2s ease;
}

.ajax-search-item:last-child {
    border-bottom: none;
}

.ajax-search-item:hover,
.ajax-search-item.keyboard-highlighted {
    background-color: #f9f9f9;
}

.ajax-search-item-image {
    width: 50px;
    height: 50px;
    margin-right: 15px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ajax-search-item-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.ajax-search-item-image .no-image {
    color: #ddd;
    font-size: 24px;
}

.ajax-search-item-details {
    flex-grow: 1;
}

.ajax-search-item-title {
    margin: 0 0 5px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.ajax-search-item-price {
    font-size: 13px;
    color: #666;
}

.ajax-search-highlight {
    background-color: #ffffd1;
    font-weight: bold;
}

/* View All Results Link */
.ajax-search-view-all {
    display: block;
    padding: 10px;
    text-align: center;
    background-color: #f5f5f5;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    border-top: 1px solid #eee;
}

.ajax-search-view-all:hover {
    background-color: #eee;
}

/* No Results Message */
.ajax-search-no-results {
    padding: 15px;
    text-align: center;
    color: #666;
}

/* Popular Searches */
.popular-searches-header {
    padding: 10px;
    font-weight: 500;
    color: #333;
    border-bottom: 1px solid #eee;
}

.popular-search-item {
    padding: 8px 10px;
}

.popular-search-item .search-item-content {
    display: flex;
    align-items: center;
}

.popular-search-item .popular-icon {
    margin-right: 10px;
    color: #ff6b6b;
    font-size: 14px;
}

.popular-search-item .search-item-title {
    font-size: 14px;
    color: #333;
}

/* Loading Popular Searches */
.loading-popular {
    padding: 15px;
    text-align: center;
    color: #666;
}

/* Overlay */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

.search-overlay.show {
    display: block;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .ajax-search-container {
        max-width: 100%;
    }
    
    #ajax-search-results {
        max-height: 300px;
    }
    
    .ajax-search-item-image {
        width: 40px;
        height: 40px;
        margin-right: 10px;
    }
    
    .ajax-search-item-title {
        font-size: 13px;
    }
    
    .ajax-search-item-price {
        font-size: 12px;
    }
}
